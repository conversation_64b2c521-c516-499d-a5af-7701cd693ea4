r"""
Description:
Author: li
Date: 2025-04-19 22:15:32
LastEditors: li
LastEditTime: 2025-04-19 22:15:57
FilePath: \单三相电表测试软件\stacked_widget.py
"""

import sys  # 导入系统模块，用于访问与Python解释器和环境相关的变量和函数
import os  # 导入操作系统模块，用于文件路径操作和系统交互
import struct  # 导入结构体模块，用于构建C结构体格式数据


sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "ui文件")))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "software")))


from ui文件.功率校表_ui import Ui_power_calibration
from ui文件.误差校表_ui import Ui_err_calibration
from PySide6.QtWidgets import QWidget, QSizePolicy
from typing import Dict, Any, Optional


class BaseCalibrationWindow:
    """校准窗口基类，采用鸭子类型思想，提供通用功能。"""

    def __init__(self):
        self.parent_window = None

    def get_cali_params_from_ui(self) -> dict:
        """从界面获取所有参数并返回字典(使用UI文本作为键) - 子类应重写此方法"""
        raise NotImplementedError("子类必须实现 get_cali_params_from_ui 方法")

    def apply_params_to_ui(self, data_dict):
        """将字典数据应用到UI控件上（动态属性访问） - 子类应重写此方法"""
        raise NotImplementedError("子类必须实现 apply_params_to_ui 方法")

    def build_calibrate_data_struct(
        self, u_rms: list, i_rms: list, power_p: list, power_q: list, power_s: list, err: Optional[list] = None
    ) -> bytes:
        """构建校表数据bytes

        Args:
            u_rms: 各相电压值列表 [A相, B相, C相]
            i_rms: 各相电流值列表 [A相, B相, C相, N相]
            power_p: 各相有功功率列表 [A相, B相, C相]
            power_q: 各相无功功率列表 [A相, B相, C相]
            power_s: 各相视在功率列表 [A相, B相, C相]
            err: 各相计量误差列表 [A相, B相, C相]，默认为[0,0,0]

        Returns:
            bytes: 打包好的校准数据
        """
        try:
            if err is None:
                err = [0.0, 0.0, 0.0]

            # 使用 struct 打包数据，按照 C 结构体格式
            # 格式字符串：3f (Urms) + 4f (Irms) + 3f (power_p) + 3f (power_q) + 3f (power_s) + 3f (err)
            calibrate_data = struct.pack("3f4f3f3f3f3f", *u_rms, *i_rms, *power_p, *power_q, *power_s, *err)

            return calibrate_data

        except Exception as e:
            print(f"构建结构体失败: {str(e)}")
            return b""

    def build_mfc_calibrate_data_struct(
        self,
        cmd: int,
        const_mul: int = 0,
        u_rms: Optional[list] = None,
        i_rms: Optional[list] = None,
        power_p: Optional[list] = None,
        power_q: Optional[list] = None,
        power_s: Optional[list] = None,
        err: Optional[list] = None,
    ) -> bytes:
        """构建 MfcCalibrateData_s 结构体数据

        Args:
            cmd: 命令码
            const_mul: 常数乘数
            u_rms: 各相电压值
            i_rms: 各相电流值
            power_p: 各相有功功率
            power_q: 各相无功功率
            power_s: 各相视在功率
            err: 各相计量误差

        Returns:
            bytes: 打包好的MFC校准数据
        """
        try:
            # 构建数据域部分
            calibrate_data = self.build_calibrate_data_struct(
                u_rms or [], i_rms or [], power_p or [], power_q or [], power_s or [], err
            )
            if not calibrate_data:
                return b""

            # 构建 MfcCalibrateData_s 结构体
            # 格式：cmd (B) + const_mul (B) + Reserved[2] (2B) + CalibrateData_s
            reserved = b"\x00\x00"  # 预留字节

            # 打包结构体数据
            mfc_data = struct.pack("BB", cmd, const_mul) + reserved + calibrate_data

            return mfc_data

        except Exception as e:
            print(f"构建MFC结构体失败: {str(e)}")
            return b""

    def save_cali_params_to_json(self):
        """将参数保存到json文件"""
        # self.parent.file_handler.save_json_data(self.get_cali_params_from_ui())
        
        if (
           self.parent_window and hasattr(self.parent_window, "file_handler")
           and self.parent_window.file_handler is not None
           and hasattr(self.parent_window.file_handler, "save_json_data")
           and self.parent_window.file_handler.save_json_data is not None
        ):
            self.parent_window.file_handler.save_json_data(self.get_cali_params_from_ui())
        
        if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
            self.parent_window.display_to_log_edit("参数已保存到json文件")
        else:
            print("参数已保存到json文件")

    def get_cali_params_from_json(self):
        """从json文件中设置参数到界面"""
        # 子类需要实现get_cali_params_from_ui和apply_params_to_ui方法
        try:
            if hasattr(self, "get_cali_params_from_ui") and hasattr(self, "apply_params_to_ui"):
                cali_dict = self.get_cali_params_from_ui()
                if self.parent_window and hasattr(self.parent_window, "file_handler"):
                    cali_dict = self.parent_window.file_handler.load_json_data(type_dict=cali_dict)
                else:
                    print("从json文件中读取参数失败")

                self.apply_params_to_ui(cali_dict)
            else:
                print("子类需要实现get_cali_params_from_ui和apply_params_to_ui方法")
        except AttributeError:
            print("子类需要实现get_cali_params_from_ui和apply_params_to_ui方法")
        except Exception as e:
            print(f"从JSON获取参数失败: {str(e)}")

    def build_dlt645_frame(
        self, cmd_code: int, u_rms: list, i_rms: list, power_p: list, power_q: list, power_s: list, err: list
    ) -> Optional[bytes]:
        """构建DLT645协议帧（通用版本）

        Args:
            cmd_code: 命令码
            u_rms: 各相电压值
            i_rms: 各相电流值
            power_p: 各相有功功率
            power_q: 各相无功功率
            power_s: 各相视在功率
            err: 各相计量误差

        Returns:
            bytes: DLT645协议帧数据，失败时返回None
        """
        try:
            if not self.parent_window or not hasattr(self.parent_window, "build_protocol"):
                return None

            # 检查，地址是不是12个纯数字，然后地址两两一组转换成6字节数据
            if not hasattr(self.parent_window, "addr_edit"):
                if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                    self.parent_window.display_to_log_edit("父窗口没有addr_edit属性", "red")
                return None

            addr_text = self.parent_window.addr_edit.text().strip()

            # 验证地址格式：必须是12位十六进制字符（0-9）
            if not all(c in "0123456789aA" for c in addr_text) or len(addr_text) != 12:
                if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                    self.parent_window.display_to_log_edit("地址格式错误：必须是12位十六进制字符", "red")
                return None

            # 将12位十六进制地址转换为6字节数据（两两一组），并进行大小端转换
            addr_bytes = bytearray()
            for i in range(0, 12, 2):
                # 每两位十六进制字符转换为一个字节
                hex_pair = addr_text[i : i + 2]
                byte_val = int(hex_pair, 16)
                addr_bytes.append(byte_val)

            # 进行大小端转换（字节顺序反转）
            addr_bytes.reverse()
            addr = bytes(addr_bytes)

            # 构建结构体数据
            data = self.build_mfc_calibrate_data_struct(
                cmd=cmd_code, u_rms=u_rms, i_rms=i_rms, power_p=power_p, power_q=power_q, power_s=power_s, err=err
            )
            if not data:
                return None

            # 打印调试信息
            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                hex_str = " ".join([f"{x:02X}" for x in data])
                self.parent_window.display_to_log_edit(f"MFC数据: {hex_str}")

            dlt645_cmd = 0x1F
            params: Dict[str, Any] = {"addr": addr, "cmd_code": dlt645_cmd, "data": data}
            cmd_frame = self.parent_window.build_protocol(params)

            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit(
                    f"下发步骤：0x{cmd_code:02X}({cmd_code})，数据长度：{len(data)}字节"
                )
            return cmd_frame

        except Exception as e:
            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit(f"构建协议帧失败: {str(e)}", "red")
            return None

    def send_calibration_frame(
        self, cmd_code: int, u_rms: list, i_rms: list, power_p: list, power_q: list, power_s: list, err: list
    ) -> bool:
        """发送校准数据帧

        Args:
            cmd_code: 命令码
            u_rms: 各相电压值
            i_rms: 各相电流值
            power_p: 各相有功功率
            power_q: 各相无功功率
            power_s: 各相视在功率
            err: 各相计量误差

        Returns:
            bool: 发送是否成功
        """
        try:
            cmd_frame = self.build_dlt645_frame(cmd_code, u_rms, i_rms, power_p, power_q, power_s, err)
            if cmd_frame:
                if self.parent_window and hasattr(self.parent_window, "send_dlt645_frame"):
                    self.parent_window.send_dlt645_frame(cmd_frame)
                    return True
        except Exception as e:
            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit(f"发送数据失败: {str(e)}", "red")
        return False


class PowerCalibrationWindow(QWidget, Ui_power_calibration, BaseCalibrationWindow):
    def __init__(self, parent=None):
        super().__init__(parent)  # 传递 parent 参数给 QWidget 的 __init__
        self.setupUi(self)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        if self.layout():
            self.layout().setContentsMargins(0, 0, 0, 0)  # type: ignore
            self.layout().setSpacing(0)  # type: ignore
        self.parent_window = parent
        # 连接按钮事件
        self.pushB_start_cali.clicked.connect(self.power_cali_dlt645_frame)

        # 新的预设方案按钮事件
        self.load_preset_btn.clicked.connect(self.load_preset)
        self.save_preset_btn.clicked.connect(self.save_preset)

    def load_preset(self):
        """加载选定的预设方案"""
        if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
            self.parent_window.display_to_log_edit("加载方案功能待实现", "blue")

    def save_preset(self):
        """保存当前UI状态为新的预设方案"""
        if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
            self.parent_window.display_to_log_edit("保存方案功能待实现", "blue")

    def get_cali_params_from_ui(self) -> dict:
        """从界面获取所有参数并返回字典(使用UI文本作为键)"""
        params = {
            self.windowTitle(): {
                self.label_a.text(): {
                    self.label_v.text(): self.lineEdit_a_v.text(),
                    self.label_i.text(): self.lineEdit_a_i.text(),
                    self.label_p.text(): self.lineEdit_a_p.text(),
                    self.label_q.text(): self.lineEdit_a_q.text(),
                    self.label_s.text(): self.lineEdit_a_s.text(),
                    self.label_z.text(): self.lineEdit_a_z.text(),
                },
                self.label_b.text(): {
                    self.label_v.text(): self.lineEdit_b_v.text(),
                    self.label_i.text(): self.lineEdit_b_i.text(),
                    self.label_p.text(): self.lineEdit_b_p.text(),
                    self.label_q.text(): self.lineEdit_b_q.text(),
                    self.label_s.text(): self.lineEdit_b_s.text(),
                    self.label_z.text(): self.lineEdit_b_z.text(),
                },
                self.label_c.text(): {
                    self.label_v.text(): self.lineEdit_c_v.text(),
                    self.label_i.text(): self.lineEdit_c_i.text(),
                    self.label_p.text(): self.lineEdit_c_p.text(),
                    self.label_q.text(): self.lineEdit_c_q.text(),
                    self.label_s.text(): self.lineEdit_c_s.text(),
                    self.label_z.text(): self.lineEdit_c_z.text(),
                },
            }
        }
        return params

    def apply_params_to_ui(self, data_dict):
        """将字典数据应用到UI控件上（动态属性访问）"""
        try:
            window_data = data_dict.get(self.windowTitle(), {})
            # 相位映射
            phases = {self.label_a.text(): "a", self.label_b.text(): "b", self.label_c.text(): "c"}
            # 参数映射
            params = {
                self.label_v.text(): "v",
                self.label_i.text(): "i",
                self.label_p.text(): "p",
                self.label_q.text(): "q",
                self.label_s.text(): "s",
                self.label_z.text(): "z",
            }

            # 遍历相位
            for phase_label, phase_data in window_data.items():
                # 遍历参数
                for param_label, value in phase_data.items():
                    # 动态构造控件名称
                    widget_name = f"lineEdit_{phases[phase_label]}_{params[param_label]}"

                    # 如果控件存在，设置其值
                    if hasattr(self, widget_name):
                        widget = getattr(self, widget_name)
                        widget.setText(str(value))

            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit("参数已应用到界面")
            else:
                print("参数已应用到界面")
        except Exception as e:
            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit(f"应用参数失败: {str(e)}", "red")
            else:
                print(f"应用参数失败: {str(e)}")

    def power_cali_dlt645_frame(self):
        """发送功率校准数据"""
        try:
            cmd_code_str = self.cali_step_edit.text().strip()
            cmd_code = None

            # 检查指令代码是否为空或无效
            if not cmd_code_str:
                if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                    self.parent_window.display_to_log_edit("请输入指令代码", "red")
                return False

            # 尝试解析十六进制或十进制
            try:
                if cmd_code_str.lower().startswith("0x"):
                    cmd_code = int(cmd_code_str, 16)
                elif cmd_code_str.lower().endswith("h"):
                    cmd_code = int(cmd_code_str[:-1], 16)
                else:
                    cmd_code = int(cmd_code_str)
            except ValueError:
                if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                    self.parent_window.display_to_log_edit(f"指令代码格式错误: '{cmd_code_str}'", "red")
                return False
            if cmd_code is None:
                return False

            # 从UI获取各相参数
            u_rms = [
                float(self.lineEdit_a_v.text() or 0),
                float(self.lineEdit_b_v.text() or 0),
                float(self.lineEdit_c_v.text() or 0),
            ]
            i_rms = [
                float(self.lineEdit_a_i.text() or 0),
                float(self.lineEdit_b_i.text() or 0),
                float(self.lineEdit_c_i.text() or 0),
                float(self.lineEdit_c_z.text() or 0),
            ]
            power_p = [
                float(self.lineEdit_a_p.text() or 0),
                float(self.lineEdit_b_p.text() or 0),
                float(self.lineEdit_c_p.text() or 0),
            ]
            power_q = [
                float(self.lineEdit_a_q.text() or 0),
                float(self.lineEdit_b_q.text() or 0),
                float(self.lineEdit_c_q.text() or 0),
            ]
            power_s = [
                float(self.lineEdit_a_s.text() or 0),
                float(self.lineEdit_b_s.text() or 0),
                float(self.lineEdit_c_s.text() or 0),
            ]
            err = [0.0, 0.0, 0.0]

            # 使用基类方法发送校准帧
            return self.send_calibration_frame(cmd_code, u_rms, i_rms, power_p, power_q, power_s, err)

        except Exception as e:
            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit(f"发送数据失败: {str(e)}", "red")
            return False


# 自定义误差校表窗口类
class ErrorCalibrationWindow(QWidget, Ui_err_calibration, BaseCalibrationWindow):
    def __init__(self, parent=None):
        super().__init__(parent)  # 传递 parent 参数给 QWidget 的 __init__
        self.setupUi(self)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        if self.layout():
            self.layout().setContentsMargins(0, 0, 0, 0)  # type: ignore
            self.layout().setSpacing(0)  # type: ignore
        self.parent_window = parent

    def get_cali_params_from_ui(self):
        """从界面获取所有参数并返回字典(使用UI文本作为键)"""
        params = {
            self.windowTitle(): {
                # self.label_a.text(): {
                #     self.label_v.text(): self.lineEdit_a_v.text(),
                #     self.label_i.text(): self.lineEdit_a_i.text(),
                #     self.label_p.text(): self.lineEdit_a_p.text(),
                #     self.label_q.text(): self.lineEdit_a_q.text(),
                #     self.label_s.text(): self.lineEdit_a_s.text(),
                #     self.label_z.text(): self.lineEdit_a_z.text(),
                # },
                # self.label_b.text(): {
                #     self.label_v.text(): self.lineEdit_b_v.text(),
                #     self.label_i.text(): self.lineEdit_b_i.text(),
                #     self.label_p.text(): self.lineEdit_b_p.text(),
                #     self.label_q.text(): self.lineEdit_b_q.text(),
                #     self.label_s.text(): self.lineEdit_b_s.text(),
                #     self.label_z.text(): self.lineEdit_b_z.text(),
                # },
                # self.label_c.text(): {
                #     self.label_v.text(): self.lineEdit_c_v.text(),
                #     self.label_i.text(): self.lineEdit_c_i.text(),
                #     self.label_p.text(): self.lineEdit_c_p.text(),
                #     self.label_q.text(): self.lineEdit_c_q.text(),
                #     self.label_s.text(): self.lineEdit_c_s.text(),
                #     self.label_z.text(): self.lineEdit_c_z.text(),
                # },
            }
        }
        return params

    def apply_params_to_ui(self, data_dict):
        """将字典数据应用到UI控件上（动态属性访问）"""
        try:
            window_data = data_dict.get(self.windowTitle(), {})
            # # 相位映射
            # phases = {self.label_a.text(): "a", self.label_b.text(): "b", self.label_c.text(): "c"}
            # # 参数映射
            # params = {
            #     self.label_v.text(): "v",
            #     self.label_i.text(): "i",
            #     self.label_p.text(): "p",
            #     self.label_q.text(): "q",
            #     self.label_s.text(): "s",
            #     self.label_z.text(): "z",
            # }
            # 遍历相位
            # for phase_label, phase_data in window_data.items():
            #     # 遍历参数
            #     for param_label, value in phase_data.items():
            #         # 动态构造控件名称
            #         widget_name = f"lineEdit_{phases[phase_label]}_{params[param_label]}"
            #         # 如果控件存在，设置其值
            #         if hasattr(self, widget_name):
            #             widget = getattr(self, widget_name)
            #             widget.setText(str(value))
            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit("参数已应用到界面")
            else:
                print("参数已应用到界面")
        except Exception as e:
            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit(f"应用参数失败: {str(e)}", "red")
            else:
                print(f"应用参数失败: {str(e)}")

    def save_cali_params_to_txt(self):
        """将参数保存到txt文件"""
        if self.parent_window and hasattr(self.parent_window, "file_handler"):
            self.parent_window.file_handler.save_json_data(self.get_cali_params_from_ui())

    def get_cali_params_from_json(self):
        """从json文件中设置参数到界面"""
        cali_dict = self.get_cali_params_from_ui()
        if self.parent_window and hasattr(self.parent_window, "file_handler"):
            cali_dict = self.parent_window.file_handler.load_json_data(type_dict=cali_dict)
        self.apply_params_to_ui(cali_dict)
