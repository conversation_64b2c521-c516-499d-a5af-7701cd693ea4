r"""
Author: li <EMAIL>                # 作者信息及邮箱
Date: 2024-12-30 22:29:15                  # 创建日期
LastEditors: li <EMAIL>           # 最后编辑者信息
LastEditTime: 2025-08-29 22:12:35          # 最后编辑时间
FilePath: \单三相电表测试软件\software\test_fuc.py  # 文件路径
"""

# 测试代码，仅在直接运行时执行，用于测试函数功能
if __name__ == "__main__":
    print("test python")

    it = iter([1, 2, 3])
    print("__next__" in dir(it))  # True，说明是迭代器

    numbers = [1, 2, 3, 4, 5]
    squared = list(map(lambda x: x**2, numbers))
    print(squared)

    # from typing import Protocol

    # class multi_debug(Protocol):
    #     def debug(self, msg: str) -> bool:
    #         ...

    # class debug_s:
    #     def debug(self, msg: str) -> bool:
    #         print(f"debug: {msg}")
    #         return True

    # class user_debug:
    #     def debug(self, msg: str) -> bool:
    #         print(f"user_debug: {msg}")
    #         return True

    # def print_debug(obj: multi_debug, msg: str) -> None:
    #     obj.debug(msg)

    # print_debug(debug_s(), "hello")
    # print_debug(user_debug(), "hello")


    import struct
    
    print("=" * 50)
    print("struct 模块用法示例")
    print("=" * 50)
    
    # 1. 基本数据类型打包和解包
    print("\n1. 基本数据类型打包和解包:")
    
    hex_data = b"\x00\x00\x5C\x43"
    little_result = struct.unpack('<f', hex_data)[0]
    print(f"小端解包: {little_result}")
    
    # 打包整数
    packed_int = struct.pack('i', 12345)  # 4字节整数
    print(f"打包整数 12345: {packed_int.hex()}")
    
    # 解包整数
    unpacked_int = struct.unpack('i', packed_int)[0]
    print(f"解包整数: {unpacked_int}")
    
    # 打包浮点数
    packed_float = struct.pack('f', 3.14159)  # 4字节浮点数
    print(f"打包浮点数 3.14159: {packed_float.hex()}")
    
    # 解包浮点数
    unpacked_float = struct.unpack('f', packed_float)[0]
    print(f"解包浮点数: {unpacked_float:.6f}")
    
    # 2. 字节序处理（小端和大端）
    print("\n2. 字节序处理:")
    
    value = 0x12345678
    little_endian = struct.pack('<I', value)  # 小端
    big_endian = struct.pack('>I', value)     # 大端
    
    print(f"原始值: 0x{value:08X}")
    print(f"小端字节序: {little_endian.hex()}")
    print(f"大端字节序: {big_endian.hex()}")
    
    # 3. 多个值同时打包
    print("\n3. 多个值同时打包:")
    
    # 打包多个不同类型的数据
    packed_data = struct.pack('if?', 100, 2.71828, True)  # 整数、浮点数、布尔值
    print(f"打包多个值: {packed_data.hex()}")
    
    # 解包多个值
    unpacked_data = struct.unpack('if?', packed_data)
    print(f"解包多个值: {unpacked_data}")
    
    # 4. 十六进制字符串处理
    print("\n4. 十六进制字符串处理:")
    
    hex_string = "00 00 5C 43"  # 220.0 的小端浮点数表示
    b = bytes.fromhex(hex_string.replace(" ", ""))
    
    # 小端解包
    little_result = struct.unpack('<f', b)[0]
    print(f"十六进制 '{hex_string}' 小端解包: {little_result}")
    
    # 大端解包
    big_result = struct.unpack('>f', b)[0]
    print(f"十六进制 '{hex_string}' 大端解包: {big_result}")
    
    # 5. 计算打包后的大小
    print("\n5. 计算打包后的大小:")
    
    format_str = 'if?d'  # 整数、浮点数、布尔值、双精度浮点数
    calc_size = struct.calcsize(format_str)
    print(f"格式 '{format_str}' 的打包大小: {calc_size} 字节")
    
    # 6. 处理字符串
    print("\n6. 处理字符串:")
    
    # 打包字符串（需要指定长度）
    name = "hello"
    packed_str = struct.pack('5s', name.encode())  # 5个字符的字符串
    print(f"打包字符串 '{name}': {packed_str.hex()}")
    
    # 解包字符串
    unpacked_str = struct.unpack('5s', packed_str)[0].decode().strip('\x00')
    print(f"解包字符串: '{unpacked_str}'")
    
    # 7. 复杂数据结构
    print("\n7. 复杂数据结构:")
    
    # 打包结构体数据
    data_struct = struct.pack('H8sf', 123, b'testdata', 99.5)  # 无符号短整型、8字节字符串、浮点数
    print(f"打包结构体: {data_struct.hex()}")

    # 解包结构体
    unpacked_struct = struct.unpack('H8sf', data_struct)
    print(f"解包结构体: {unpacked_struct}")
    print(f"字符串部分: {unpacked_struct[1].decode().strip(chr(0))}")
    
    print("\n" + "=" * 50)
    print("struct 模块常用格式字符:")
    print("=" * 50)
    print("x: 填充字节")
    print("b: 有符号字节")
    print("B: 无符号字节")
    print("h: 有符号短整型 (2字节)")
    print("H: 无符号短整型 (2字节)")
    print("i: 有符号整型 (4字节)")
    print("I: 无符号整型 (4字节)")
    print("l: 有符号长整型 (4字节)")
    print("L: 无符号长整型 (4字节)")
    print("q: 有符号长长整型 (8字节)")
    print("Q: 无符号长长整型 (8字节)")
    print("f: 单精度浮点数 (4字节)")
    print("d: 双精度浮点数 (8字节)")
    print("s: 字符串 (需要指定长度)")
    print("?: 布尔值 (1字节)")
    print("字节序前缀:")
    print("<: 小端字节序")
    print(">: 大端字节序")
    print("!: 网络字节序 (大端)")
    print("=: 原生字节序")
