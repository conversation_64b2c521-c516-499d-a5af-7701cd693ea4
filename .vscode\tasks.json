{"version": "2.0.0", "tasks": [{"label": "运行电表测试软件", "type": "shell", "command": "python", "args": ["main.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "打包电表测试软件 (使用spec)", "type": "shell", "command": "pyinstaller", "args": ["--noconfirm", "校表软件.spec"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "打包电表测试软件 (命令行方式)", "type": "shell", "command": "pyinstaller", "args": ["--name=校表软件", "--windowed", "--noconfirm", "--icon=icons/app.ico", "--add-data=ui文件:ui文件", "--add-data=software:software", "main.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "清理打包文件", "type": "shell", "command": "powershell", "args": ["-Command", "Remove-Item -Recurse -Force -ErrorAction SilentlyContinue build; Remove-Item -Recurse -Force -ErrorAction SilentlyContinue dist"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "安装依赖", "type": "shell", "command": "pip", "args": ["install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}