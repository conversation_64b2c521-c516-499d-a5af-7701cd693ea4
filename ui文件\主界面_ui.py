# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file '主界面.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, Q<PERSON><PERSON>r, Q<PERSON><PERSON>al<PERSON>rad<PERSON>, Q<PERSON>ursor,
    Q<PERSON><PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QGridLayout, QHBoxLayout,
    QLineEdit, QPushButton, QSizePolicy, QSpacerItem,
    QStackedWidget, QTextEdit, QWidget)

class Ui_main_page(object):
    def setupUi(self, main_page):
        if not main_page.objectName():
            main_page.setObjectName(u"main_page")
        main_page.resize(967, 1000)
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Maximum, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(main_page.sizePolicy().hasHeightForWidth())
        main_page.setSizePolicy(sizePolicy)
        self.gridLayout_2 = QGridLayout(main_page)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout = QGridLayout()
        self.gridLayout.setObjectName(u"gridLayout")
        self.port_box = QComboBox(main_page)
        self.port_box.addItem("")
        self.port_box.addItem("")
        self.port_box.setObjectName(u"port_box")
        font = QFont()
        font.setPointSize(15)
        self.port_box.setFont(font)
        self.port_box.setEditable(False)

        self.gridLayout.addWidget(self.port_box, 0, 0, 1, 1)

        self.band_rate_box = QComboBox(main_page)
        self.band_rate_box.addItem("")
        self.band_rate_box.addItem("")
        self.band_rate_box.addItem("")
        self.band_rate_box.addItem("")
        self.band_rate_box.setObjectName(u"band_rate_box")
        self.band_rate_box.setFont(font)
        self.band_rate_box.setEditable(False)

        self.gridLayout.addWidget(self.band_rate_box, 0, 1, 1, 1)

        self.even_box = QComboBox(main_page)
        self.even_box.addItem("")
        self.even_box.addItem("")
        self.even_box.addItem("")
        self.even_box.setObjectName(u"even_box")
        self.even_box.setFont(font)
        self.even_box.setEditable(False)

        self.gridLayout.addWidget(self.even_box, 0, 2, 1, 1)

        self.data_len_box = QComboBox(main_page)
        self.data_len_box.addItem("")
        self.data_len_box.addItem("")
        self.data_len_box.setObjectName(u"data_len_box")
        self.data_len_box.setFont(font)
        self.data_len_box.setEditable(False)

        self.gridLayout.addWidget(self.data_len_box, 0, 3, 1, 1)

        self.stop_bits_box = QComboBox(main_page)
        self.stop_bits_box.addItem("")
        self.stop_bits_box.addItem("")
        self.stop_bits_box.addItem("")
        self.stop_bits_box.setObjectName(u"stop_bits_box")
        self.stop_bits_box.setFont(font)
        self.stop_bits_box.setEditable(False)

        self.gridLayout.addWidget(self.stop_bits_box, 0, 4, 1, 1)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout.addItem(self.horizontalSpacer_2, 0, 5, 1, 2)

        self.refresh_btn = QPushButton(main_page)
        self.refresh_btn.setObjectName(u"refresh_btn")
        self.refresh_btn.setFont(font)
        icon = QIcon(QIcon.fromTheme(QIcon.ThemeIcon.ViewRefresh))
        self.refresh_btn.setIcon(icon)
        self.refresh_btn.setAutoDefault(False)

        self.gridLayout.addWidget(self.refresh_btn, 1, 0, 1, 1)

        self.set_serial_1_btn = QPushButton(main_page)
        self.set_serial_1_btn.setObjectName(u"set_serial_1_btn")
        self.set_serial_1_btn.setFont(font)
        self.set_serial_1_btn.setAutoDefault(False)

        self.gridLayout.addWidget(self.set_serial_1_btn, 1, 1, 1, 1)

        self.set_serial_2_btn = QPushButton(main_page)
        self.set_serial_2_btn.setObjectName(u"set_serial_2_btn")
        self.set_serial_2_btn.setFont(font)
        self.set_serial_2_btn.setAutoDefault(False)

        self.gridLayout.addWidget(self.set_serial_2_btn, 1, 2, 1, 1)

        self.open_btn = QPushButton(main_page)
        self.open_btn.setObjectName(u"open_btn")
        self.open_btn.setFont(font)
        self.open_btn.setAutoDefault(False)

        self.gridLayout.addWidget(self.open_btn, 1, 3, 1, 1)

        self.read_addr_btn = QPushButton(main_page)
        self.read_addr_btn.setObjectName(u"read_addr_btn")
        self.read_addr_btn.setMaximumSize(QSize(150, 16777215))
        self.read_addr_btn.setFont(font)
        self.read_addr_btn.setAutoDefault(False)

        self.gridLayout.addWidget(self.read_addr_btn, 1, 4, 1, 2)

        self.addr_edit = QLineEdit(main_page)
        self.addr_edit.setObjectName(u"addr_edit")
        self.addr_edit.setMaximumSize(QSize(180, 16777215))
        self.addr_edit.setFont(font)

        self.gridLayout.addWidget(self.addr_edit, 1, 6, 1, 2)

        self.power_cali_btn = QPushButton(main_page)
        self.power_cali_btn.setObjectName(u"power_cali_btn")
        self.power_cali_btn.setFont(font)

        self.gridLayout.addWidget(self.power_cali_btn, 2, 0, 1, 1)

        self.err_cali_btn = QPushButton(main_page)
        self.err_cali_btn.setObjectName(u"err_cali_btn")
        self.err_cali_btn.setFont(font)

        self.gridLayout.addWidget(self.err_cali_btn, 2, 1, 1, 1)

        self.read_meter_chip_btn = QPushButton(main_page)
        self.read_meter_chip_btn.setObjectName(u"read_meter_chip_btn")
        self.read_meter_chip_btn.setFont(font)

        self.gridLayout.addWidget(self.read_meter_chip_btn, 2, 2, 1, 1)

        self.cali_data_btn = QPushButton(main_page)
        self.cali_data_btn.setObjectName(u"cali_data_btn")
        self.cali_data_btn.setFont(font)

        self.gridLayout.addWidget(self.cali_data_btn, 2, 3, 1, 1)

        self.db_viewer_btn = QPushButton(main_page)
        self.db_viewer_btn.setObjectName(u"db_viewer_btn")
        self.db_viewer_btn.setFont(font)

        self.gridLayout.addWidget(self.db_viewer_btn, 2, 4, 1, 1)

        self.dlt645_sub_33_btn = QPushButton(main_page)
        self.dlt645_sub_33_btn.setObjectName(u"dlt645_sub_33_btn")
        self.dlt645_sub_33_btn.setFont(font)

        self.gridLayout.addWidget(self.dlt645_sub_33_btn, 2, 5, 1, 2)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout.addItem(self.horizontalSpacer_3, 2, 7, 1, 1)


        self.gridLayout_2.addLayout(self.gridLayout, 0, 0, 1, 1)

        self.stackedWidget = QStackedWidget(main_page)
        self.stackedWidget.setObjectName(u"stackedWidget")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.stackedWidget.sizePolicy().hasHeightForWidth())
        self.stackedWidget.setSizePolicy(sizePolicy1)
        self.stackedWidget.setMinimumSize(QSize(0, 180))
        self.stackedWidget.setMaximumSize(QSize(16777215, 350))

        self.gridLayout_2.addWidget(self.stackedWidget, 1, 0, 1, 1)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer)

        self.clear_log_btn = QPushButton(main_page)
        self.clear_log_btn.setObjectName(u"clear_log_btn")
        self.clear_log_btn.setFont(font)

        self.horizontalLayout.addWidget(self.clear_log_btn)


        self.gridLayout_2.addLayout(self.horizontalLayout, 2, 0, 1, 1)

        self.log_edit = QTextEdit(main_page)
        self.log_edit.setObjectName(u"log_edit")
        self.log_edit.setMinimumSize(QSize(0, 500))
        font1 = QFont()
        font1.setPointSize(13)
        self.log_edit.setFont(font1)

        self.gridLayout_2.addWidget(self.log_edit, 3, 0, 1, 1)

        QWidget.setTabOrder(self.port_box, self.band_rate_box)
        QWidget.setTabOrder(self.band_rate_box, self.data_len_box)
        QWidget.setTabOrder(self.data_len_box, self.even_box)
        QWidget.setTabOrder(self.even_box, self.stop_bits_box)

        self.retranslateUi(main_page)

        self.even_box.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(main_page)
    # setupUi

    def retranslateUi(self, main_page):
        main_page.setWindowTitle(QCoreApplication.translate("main_page", u"\u6821\u8868\u8f6f\u4ef6", None))
        self.port_box.setItemText(0, QCoreApplication.translate("main_page", u"com1", None))
        self.port_box.setItemText(1, QCoreApplication.translate("main_page", u"com2", None))

        self.port_box.setCurrentText(QCoreApplication.translate("main_page", u"com1", None))
        self.band_rate_box.setItemText(0, QCoreApplication.translate("main_page", u"2400", None))
        self.band_rate_box.setItemText(1, QCoreApplication.translate("main_page", u"4800", None))
        self.band_rate_box.setItemText(2, QCoreApplication.translate("main_page", u"9600", None))
        self.band_rate_box.setItemText(3, QCoreApplication.translate("main_page", u"115200", None))

        self.band_rate_box.setCurrentText(QCoreApplication.translate("main_page", u"2400", None))
        self.even_box.setItemText(0, QCoreApplication.translate("main_page", u"\u5076\u6821\u9a8c", None))
        self.even_box.setItemText(1, QCoreApplication.translate("main_page", u"\u65e0\u6821\u9a8c", None))
        self.even_box.setItemText(2, QCoreApplication.translate("main_page", u"\u5947\u6821\u9a8c", None))

        self.even_box.setCurrentText(QCoreApplication.translate("main_page", u"\u5076\u6821\u9a8c", None))
        self.data_len_box.setItemText(0, QCoreApplication.translate("main_page", u"8\u4f4d", None))
        self.data_len_box.setItemText(1, QCoreApplication.translate("main_page", u"7\u4f4d", None))

        self.data_len_box.setCurrentText(QCoreApplication.translate("main_page", u"8\u4f4d", None))
        self.stop_bits_box.setItemText(0, QCoreApplication.translate("main_page", u"1\u4f4d", None))
        self.stop_bits_box.setItemText(1, QCoreApplication.translate("main_page", u"1.5\u4f4d", None))
        self.stop_bits_box.setItemText(2, QCoreApplication.translate("main_page", u"2\u4f4d", None))

        self.stop_bits_box.setCurrentText(QCoreApplication.translate("main_page", u"1\u4f4d", None))
        self.refresh_btn.setText(QCoreApplication.translate("main_page", u"\u5237\u65b0\u4e32\u53e3", None))
        self.set_serial_1_btn.setText(QCoreApplication.translate("main_page", u"\u4e32\u53e3\u65b9\u6848115200", None))
        self.set_serial_2_btn.setText(QCoreApplication.translate("main_page", u"\u4e32\u53e3\u65b9\u68482400", None))
        self.open_btn.setText(QCoreApplication.translate("main_page", u"\u6253\u5f00\u4e32\u53e3", None))
        self.read_addr_btn.setText(QCoreApplication.translate("main_page", u"\u8bfb\u8bbe\u5907\u5730\u5740", None))
        self.addr_edit.setText(QCoreApplication.translate("main_page", u"AAAAAAAAAAAA", None))
        self.addr_edit.setPlaceholderText(QCoreApplication.translate("main_page", u"#\u8bbe\u5907\u5730\u5740", None))
        self.power_cali_btn.setText(QCoreApplication.translate("main_page", u"\u529f\u7387\u6821\u8868", None))
        self.err_cali_btn.setText(QCoreApplication.translate("main_page", u"\u8bef\u5dee\u6821\u8868", None))
        self.read_meter_chip_btn.setText(QCoreApplication.translate("main_page", u"\u8bfb\u5199\u8ba1\u91cf\u82af\u7247", None))
        self.cali_data_btn.setText(QCoreApplication.translate("main_page", u"\u6821\u8868\u6570\u636e", None))
        self.db_viewer_btn.setText(QCoreApplication.translate("main_page", u"\u6570\u636e\u5e93", None))
        self.dlt645_sub_33_btn.setText(QCoreApplication.translate("main_page", u"dlt645\u6570\u636e\u5904\u7406", None))
        self.clear_log_btn.setText(QCoreApplication.translate("main_page", u"\u6e05\u9664log", None))
    # retranslateUi

