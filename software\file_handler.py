# -*- coding: utf-8 -*-
"""file_handler.py - 文件处理模块

负责校表数据的读写和日志管理。
"""

import os
import sys
import json
import datetime
from pathlib import Path
from typing import Optional

# 定义JSON文件中的用户自定义数据区标记
END_MARKER = "\n//--- 用户自定义数据区 ---//\n"

class file_handler_s:
    """TXT文件处理类，管理校表数据和日志"""

    def __init__(self, parent=None):
        """初始化文件处理器

        Args:
            parent: 父对象，用于调用serial_handler的方法
        """
        self.parent = parent
        self.init_log()

        # 创建或检查校表数据文件
        if not os.path.exists("校表数据.json"):
            self.create_new_file("校表数据.json")
        else:
            print(f"校表数据.json路径: {os.path.abspath('校表数据.json')}")

    def init_log(self):
        """初始化日志文件系统

        Returns:

        """
        # 创建年/月/日层级的日志目录
        now = datetime.datetime.now()
        log_dir = os.path.join("LOG", f"{now.year}年", f"{now.month:02d}月", f"{now.day:02d}日")
        os.makedirs(log_dir, exist_ok=True)

        # 设置日志文件名和路径
        log_filename = f"{now.strftime('%Y.%m.%d_%H.%M.%S')}.log"
        self.log_file_dir = os.path.join(log_dir, log_filename)

        # 清理旧日志并创建新日志
        self.check_log_size_and_clear()
        self.create_new_file(
            filename=log_filename,
            directory=log_dir,
        )

    def check_log_size_and_clear(self):
        """检查日志文件夹大小并清理超过4KB的旧日志

        Returns:

        """
        log_dir = os.path.join("LOG")
        total_size = 0
        file_list = []
        dirs_to_remove = set()

        # 收集所有日志文件信息
        for dirpath, _, filenames in os.walk(log_dir):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                file_info = {"path": fp, "size": os.path.getsize(fp), "ctime": os.path.getctime(fp)}
                file_list.append(file_info)
                total_size += file_info["size"]
                dirs_to_remove.add(dirpath)

        # 按创建时间排序并删除最早的日志
        file_list.sort(key=lambda x: x["ctime"])
        while total_size > 4 * 1024 and file_list:
            oldest_file = file_list.pop(0)
            try:
                total_size -= oldest_file["size"]
                os.remove(oldest_file["path"])
            except Exception:
                pass

        # 清理空文件夹
        for dirpath in dirs_to_remove:
            try:
                if os.path.exists(dirpath) and not os.listdir(dirpath):
                    os.rmdir(dirpath)
            except Exception:
                pass

    def write_log_message(self, message):
        """写入日志消息

        Args:
            message: 要记录的日志消息

        Returns:

        """
        try:
            with open(self.log_file_dir, "a", encoding="utf-8") as f:
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"[{timestamp}] {message}\n")
        except Exception as e:
            print(f"日志写入失败: {e}")

    def _safe_log_to_ui(self, message, color="white"):
        """安全地向UI日志输出消息

        Args:
            message: 要输出的消息
            color: 消息颜色，默认为白色

        Returns:

        """
        try:
            # 检查parent对象是否存在且有display_to_log_edit方法
            if (
                hasattr(self, "parent")
                and self.parent is not None
                and hasattr(self.parent, "display_to_log_edit")
                and callable(self.parent.display_to_log_edit)
            ):
                self.parent.display_to_log_edit(message, color)
            else:
                # 如果UI方法不可用，至少输出到控制台
                print(f"[UI不可用] {message}")
        except Exception as e:
            # 如果出现任何异常，输出到控制台
            print(f"[UI输出失败] {message} (错误: {e})")

    def create_new_file(self, filename: str, directory: str = "."):
        """创建新文件

        Args:
            filename: 文件名
            directory: 目录路径

        Returns:

        """
        try:
            target = Path(directory) / filename
            target.parent.mkdir(parents=True, exist_ok=True)

            if filename == "校表数据.json":
                # 创建默认值
                default_cali_dict = {}
                if (
                    hasattr(self, "parent")
                    and self.parent is not None
                    and hasattr(self.parent, "power_cali_window")
                    and self.parent.power_cali_window is not None
                    and hasattr(self.parent.power_cali_window, "get_cali_params_from_ui")
                ):
                    default_cali_dict = self.cali_param_init(self.parent.power_cali_window.get_cali_params_from_ui())

                self.save_json_data(default_cali_dict, filename=filename, directory=directory)
            else:
                # For other files like logs, create an empty file.
                with target.open("w", encoding="utf-8") as f:
                    f.write("")

            print(f"文件创建成功：{target.resolve()}")
            return str(target.resolve())

        except Exception as e:
            print(f"创建失败：{type(e).__name__} - {str(e)}")
            return None

    def open_file(self, file_path):
        """打开并读取文件

        Args:
            file_path: 文件路径

        Returns:

        """
        path = None
        try:
            path = Path(file_path).resolve()

            # 验证文件存在性
            if not path.exists() or not path.is_file():
                self.write_log_message(f"文件不存在或不是有效文件: {path}")
                return None

            # 读取文件
            with path.open("r", encoding="utf-8") as file:
                content = file.read()
                self.write_log_message(f"读取文件成功: {path} [大小: {path.stat().st_size}字节]")
                return content

        except PermissionError:
            self.write_log_message(f"权限不足: {path or file_path}")
        except UnicodeDecodeError as e:
            self.write_log_message(f"编码错误: {path or file_path} - {e}")
        except Exception as e:
            self.write_log_message(f"读取失败: {path or file_path} - {type(e).__name__}: {e}")

        return None

    def get_cali_file_path(self):
        """获取校表数据文件路径

        Returns:

        """
        # 定义搜索路径优先级
        search_locations = [Path("校表数据.json")]  # 当前目录

        # 添加PyInstaller打包环境支持
        if getattr(sys, "frozen", False):
            # PyInstaller打包环境
            search_locations.insert(0, Path(sys._MEIPASS) / "校表数据.json")  # type: ignore
        else:
            # 开发环境
            search_locations.append(Path(__file__).resolve().parent.parent / "校表数据.json")

        # 查找有效文件
        for path in search_locations:
            if path.exists() and path.is_file():
                self.write_log_message(f"找到校准文件: {path}")
                return str(path)

        self.write_log_message("校准文件未找到")
        return None

    def cali_param_init(self, cali_dict: dict):
        """初始化校准参数（待实现）

        Returns:

        """
        if "功率校表" not in cali_dict and "误差校表" not in cali_dict:
            return None

        for key, value in cali_dict.items():
            if key == "功率校表":
                for phase, data in value.items():
                    for type, value in data.items():
                        if type == "电压":
                            data[type] = "220.0"
                        elif type == "电流":
                            data[type] = "5.0"
                        elif type == "有功功率":
                            data[type] = "1100.0"
                        elif type == "无功功率":
                            data[type] = "0.0"
                        elif type == "视在功率":
                            data[type] = "1100.0"
                        elif type == "零线电流":
                            data[type] = "0.0"
                        elif type == "功率因素":
                            data[type] = "1.0"

            elif key == "误差校表":
                for phase, data in value.items():
                    for type, value in data.items():
                        if type == "电压":
                            data[type] = "220"
                        elif type == "电流":
                            data[type] = "5.k"
                        elif type == "有功功率":
                            data[type] = "1100"
                        elif type == "无功功率":
                            data[type] = "0.0"
                        elif type == "视在功率":
                            data[type] = "1100.0"
                        elif type == "零线电流":
                            data[type] = "0.0"
                        elif type == "功率因素":
                            data[type] = "1.0"
        return cali_dict

    def save_json_data(self, cali_dict, filename="校表数据.json", directory="."):
        """将字典数据保存为JSON文件

        Args:
            cali_dict: 要保存的字典数据
            filename: 文件名，默认为'校表数据.json'
            directory: 目录路径，默认为当前目录

        Returns:

        """
        try:
            target = Path(directory) / filename
            target.parent.mkdir(parents=True, exist_ok=True)

            existing_data = {}
            user_content = ""

            if target.exists():
                try:
                    with target.open("r", encoding="utf-8") as f:
                        file_content = f.read()

                    # 检查是否存在数据分割符号
                    if END_MARKER in file_content:
                        # 分割json数据和用户自定义数据区
                        json_part, user_content = file_content.split(END_MARKER, 1)
                    else:
                        json_part = file_content

                    if json_part.strip():
                        existing_data = json.loads(json_part)

                except json.JSONDecodeError as e:
                    self._safe_log_to_ui(f"读取现有JSON文件失败：JSON解析错误 - {e}")
                except Exception as e:
                    self._safe_log_to_ui(f"读取现有JSON文件失败：{type(e).__name__} - {e}")

            # 合并现有数据和新数据
            merged_data = self._deep_merge_dicts(existing_data, cali_dict) if existing_data else cali_dict

            with target.open("w", encoding="utf-8") as f:
                json.dump(merged_data, f, ensure_ascii=False, indent=4)
                f.write(END_MARKER)
                f.write(user_content)

            self._safe_log_to_ui(f"JSON文件保存成功：{target.resolve()}")
            print(f"JSON文件保存成功：{target.resolve()}")
            return str(target.resolve())
        except Exception as e:
            error_msg = f"JSON文件保存失败：{type(e).__name__} - {str(e)}"
            self._safe_log_to_ui(error_msg)
            print(error_msg)
            return None

    def _deep_merge_dicts(self, dict1, dict2):
        """深度合并两个字典，包括嵌套字典

        Args:
            dict1: 第一个字典（基础字典）
            dict2: 第二个字典（要合并的新数据）

        Returns:
            合并后的字典
        """
        result = dict1.copy()

        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                # 如果两个字典都有相同的键，且值都是字典，递归合并
                result[key] = self._deep_merge_dicts(result[key], value)
            else:
                # 最后一层，键值已经不是字典，直接覆盖
                result[key] = value

        return result

    def load_json_data(
        self, filename="校表数据.json", directory=".", type_dict: Optional[dict] = None
    ) -> Optional[dict]:
        """从JSON文件加载字典数据

        Args:
            filename: 文件名，默认为'校表数据.json'
            directory: 目录路径，默认为当前目录
            type_dict: 可选的类型字典，用于合并数据类型

        Returns:
            dict: 加载的字典数据，失败时返回None
        """
        data = None
        try:
            target = Path(directory) / filename
            if not target.exists():
                error_msg = f"JSON文件不存在：{target.resolve()}"
                self._safe_log_to_ui(error_msg)
                print(error_msg)
                return None

            with target.open("r", encoding="utf-8") as f:
                content = f.read()

            if END_MARKER in content:
                content = content.split(END_MARKER, 1)[0]

            if content.strip():
                data = json.loads(content)
            else:
                data = {}

            if type_dict is not None:
                data = self._deep_merge_dicts(type_dict, data)

            self._safe_log_to_ui(f"JSON文件加载成功：{target.resolve()}", "green")
            return data

        except AttributeError:
            print("display_to_log_edit()方法不存在")
            return data

        except json.JSONDecodeError as e:
            error_msg = f"JSON解析错误：{str(e)}"
            self._safe_log_to_ui(error_msg)
            return None

        except Exception as e:
            error_msg = f"JSON文件加载失败：{type(e).__name__} - {str(e)}"
            self._safe_log_to_ui(error_msg)
            return None


if __name__ == "__main__":
    # 测试代码
    print("file_handler.py - 测试模式")

    # 测试数据
    test_data = {
        "功率校表": {
            "A": {
                "电压": "220.0",
                "电流": "5.0",
                "有功功率": "1100.0",
                "无功功率": "0.0",
                "视在功率": "1100.0",
                "零线电流": "0.0",
                "功率因素": "1.0",
            },
            "B": {
                "电压": "220.0",
                "电流": "5.0",
                "有功功率": "1100.0",
                "无功功率": "0.0",
                "视在功率": "1100.0",
                "零线电流": "0.0",
                "功率因素": "1.0",
            },
            "C": {
                "电压": "220.0",
                "电流": "5.0",
                "有功功率": "1100.0",
                "无功功率": "0.0",
                "视在功率": "1100.0",
                "零线电流": "0.0",
                "功率因素": "1.0",
            },
        }
    }

    # 删除当前页面下的校表数据.json
    if os.path.exists("校表数据.json"):
        os.remove("校表数据.json")

    # 创建处理器实例
    handler = file_handler_s()

    data = handler.load_json_data()

    if 1:
        print("\n=== JSON方式测试 ===")

        # 测试1: 初始化数据
        print("\n1. 初始化数据:")
        json_path = handler.save_json_data(test_data)
        if json_path:
            print(f"JSON文件已保存到: {json_path}")

        # 测试2: 添加新数据
        print("\n2. 添加新数据:")
        # 创建一个新的数据字典，包含不同的键
        new_data = {"电能表参数": {"表常数": "1600", "电压参考值": "220V", "电流参考值": "11A"}}
        json_path = handler.save_json_data(new_data)
        if json_path:
            print(f"JSON文件已更新: {json_path}")
        
        # 测试3: 更新现有数据
        print("\n3. 更新现有数据:")
        # 创建一个新的数据字典，更新现有的键
        update_data = {
            "功率校表": {
                "A": {
                    "电压": "230.0"  # 更新A相电压
                }
            }
        }
        json_path = handler.save_json_data(update_data)
        if json_path:
            print(f"JSON文件已更新: {json_path}")

        # 读取并显示合并后的数据
        loaded_data = handler.load_json_data()
        if loaded_data:
            print("\n成功读取合并后的JSON数据:")

            # 显示所有顶级键
            print(f"所有顶级键: {list(loaded_data.keys())}")

            # 显示更新后的A相电压
            a_voltage = loaded_data["功率校表"]["A"]["电压"]
            print(f"A相电压(已更新): {a_voltage}")

            # 显示新添加的电能表参数
            if "电能表参数" in loaded_data:
                meter_params = loaded_data["电能表参数"]
                print(f"电能表参数: {meter_params}")

            # 不知道具体键名的情况下访问数据
            print("\n不知道具体键名的情况下访问数据:")
            # 遍历所有顶级键
            for section in loaded_data.keys():
                print(f"\u5206类: {section}")
                # 如果是字典，显示其子键
                if isinstance(loaded_data[section], dict):
                    print(f"  子键: {list(loaded_data[section].keys())}") 
        
