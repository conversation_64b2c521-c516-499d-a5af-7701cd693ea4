# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file '功率校表.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, Q<PERSON><PERSON>al<PERSON><PERSON><PERSON>, Q<PERSON>ursor,
    QF<PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QGridLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QSizePolicy,
    QSpacerItem, QWidget)

class Ui_power_calibration(object):
    def setupUi(self, power_calibration):
        if not power_calibration.objectName():
            power_calibration.setObjectName(u"power_calibration")
        power_calibration.resize(1140, 292)
        self.gridLayout_2 = QGridLayout(power_calibration)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout = QGridLayout()
        self.gridLayout.setSpacing(10)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setContentsMargins(10, 10, 10, 10)
        self.label_11 = QLabel(power_calibration)
        self.label_11.setObjectName(u"label_11")
        self.label_11.setMinimumSize(QSize(40, 25))
        self.label_11.setMaximumSize(QSize(16777215, 25))
        self.label_11.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_11, 0, 0, 2, 2)

        self.label_v = QLabel(power_calibration)
        self.label_v.setObjectName(u"label_v")
        self.label_v.setMinimumSize(QSize(0, 25))
        self.label_v.setMaximumSize(QSize(16777215, 25))
        font = QFont()
        font.setPointSize(15)
        self.label_v.setFont(font)
        self.label_v.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_v, 0, 2, 2, 1)

        self.label_i = QLabel(power_calibration)
        self.label_i.setObjectName(u"label_i")
        self.label_i.setMaximumSize(QSize(16777215, 25))
        self.label_i.setFont(font)
        self.label_i.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_i, 0, 3, 2, 1)

        self.label_p = QLabel(power_calibration)
        self.label_p.setObjectName(u"label_p")
        self.label_p.setMaximumSize(QSize(16777215, 25))
        self.label_p.setFont(font)
        self.label_p.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_p, 0, 4, 2, 1)

        self.label_q = QLabel(power_calibration)
        self.label_q.setObjectName(u"label_q")
        self.label_q.setMaximumSize(QSize(16777215, 25))
        self.label_q.setFont(font)
        self.label_q.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_q, 0, 5, 2, 1)

        self.label_s = QLabel(power_calibration)
        self.label_s.setObjectName(u"label_s")
        self.label_s.setMaximumSize(QSize(16777215, 25))
        self.label_s.setFont(font)
        self.label_s.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_s, 0, 6, 2, 1)

        self.label_z = QLabel(power_calibration)
        self.label_z.setObjectName(u"label_z")
        self.label_z.setMaximumSize(QSize(16777215, 25))
        self.label_z.setFont(font)
        self.label_z.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_z, 0, 7, 2, 1)

        self.label_a = QLabel(power_calibration)
        self.label_a.setObjectName(u"label_a")
        font1 = QFont()
        font1.setPointSize(16)
        font1.setBold(True)
        self.label_a.setFont(font1)
        self.label_a.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_a, 2, 0, 1, 2)

        self.lineEdit_a_v = QLineEdit(power_calibration)
        self.lineEdit_a_v.setObjectName(u"lineEdit_a_v")
        self.lineEdit_a_v.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_a_v, 2, 2, 1, 1)

        self.lineEdit_a_i = QLineEdit(power_calibration)
        self.lineEdit_a_i.setObjectName(u"lineEdit_a_i")
        self.lineEdit_a_i.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_a_i, 2, 3, 1, 1)

        self.lineEdit_a_p = QLineEdit(power_calibration)
        self.lineEdit_a_p.setObjectName(u"lineEdit_a_p")
        self.lineEdit_a_p.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_a_p, 2, 4, 1, 1)

        self.lineEdit_a_q = QLineEdit(power_calibration)
        self.lineEdit_a_q.setObjectName(u"lineEdit_a_q")
        self.lineEdit_a_q.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_a_q, 2, 5, 1, 1)

        self.lineEdit_a_s = QLineEdit(power_calibration)
        self.lineEdit_a_s.setObjectName(u"lineEdit_a_s")
        self.lineEdit_a_s.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_a_s, 2, 6, 1, 1)

        self.lineEdit_a_z = QLineEdit(power_calibration)
        self.lineEdit_a_z.setObjectName(u"lineEdit_a_z")
        self.lineEdit_a_z.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_a_z, 2, 7, 1, 1)

        self.label_b = QLabel(power_calibration)
        self.label_b.setObjectName(u"label_b")
        self.label_b.setFont(font1)
        self.label_b.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_b, 3, 0, 1, 2)

        self.lineEdit_b_v = QLineEdit(power_calibration)
        self.lineEdit_b_v.setObjectName(u"lineEdit_b_v")
        self.lineEdit_b_v.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_b_v, 3, 2, 1, 1)

        self.lineEdit_b_i = QLineEdit(power_calibration)
        self.lineEdit_b_i.setObjectName(u"lineEdit_b_i")
        self.lineEdit_b_i.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_b_i, 3, 3, 1, 1)

        self.lineEdit_b_p = QLineEdit(power_calibration)
        self.lineEdit_b_p.setObjectName(u"lineEdit_b_p")
        self.lineEdit_b_p.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_b_p, 3, 4, 1, 1)

        self.lineEdit_b_q = QLineEdit(power_calibration)
        self.lineEdit_b_q.setObjectName(u"lineEdit_b_q")
        self.lineEdit_b_q.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_b_q, 3, 5, 1, 1)

        self.lineEdit_b_s = QLineEdit(power_calibration)
        self.lineEdit_b_s.setObjectName(u"lineEdit_b_s")
        self.lineEdit_b_s.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_b_s, 3, 6, 1, 1)

        self.lineEdit_b_z = QLineEdit(power_calibration)
        self.lineEdit_b_z.setObjectName(u"lineEdit_b_z")
        self.lineEdit_b_z.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_b_z, 3, 7, 1, 1)

        self.label_c = QLabel(power_calibration)
        self.label_c.setObjectName(u"label_c")
        self.label_c.setFont(font1)
        self.label_c.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_c, 4, 0, 1, 2)

        self.lineEdit_c_v = QLineEdit(power_calibration)
        self.lineEdit_c_v.setObjectName(u"lineEdit_c_v")
        self.lineEdit_c_v.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_c_v, 4, 2, 1, 1)

        self.lineEdit_c_i = QLineEdit(power_calibration)
        self.lineEdit_c_i.setObjectName(u"lineEdit_c_i")
        self.lineEdit_c_i.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_c_i, 4, 3, 1, 1)

        self.lineEdit_c_p = QLineEdit(power_calibration)
        self.lineEdit_c_p.setObjectName(u"lineEdit_c_p")
        self.lineEdit_c_p.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_c_p, 4, 4, 1, 1)

        self.lineEdit_c_q = QLineEdit(power_calibration)
        self.lineEdit_c_q.setObjectName(u"lineEdit_c_q")
        self.lineEdit_c_q.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_c_q, 4, 5, 1, 1)

        self.lineEdit_c_s = QLineEdit(power_calibration)
        self.lineEdit_c_s.setObjectName(u"lineEdit_c_s")
        self.lineEdit_c_s.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_c_s, 4, 6, 1, 1)

        self.lineEdit_c_z = QLineEdit(power_calibration)
        self.lineEdit_c_z.setObjectName(u"lineEdit_c_z")
        self.lineEdit_c_z.setMinimumSize(QSize(0, 30))

        self.gridLayout.addWidget(self.lineEdit_c_z, 4, 7, 1, 1)


        self.gridLayout_2.addLayout(self.gridLayout, 0, 0, 1, 1)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.label = QLabel(power_calibration)
        self.label.setObjectName(u"label")

        self.horizontalLayout.addWidget(self.label)

        self.preset_combo = QComboBox(power_calibration)
        self.preset_combo.setObjectName(u"preset_combo")
        self.preset_combo.setFont(font)

        self.horizontalLayout.addWidget(self.preset_combo)

        self.load_preset_btn = QPushButton(power_calibration)
        self.load_preset_btn.setObjectName(u"load_preset_btn")
        self.load_preset_btn.setFont(font)

        self.horizontalLayout.addWidget(self.load_preset_btn)

        self.save_preset_btn = QPushButton(power_calibration)
        self.save_preset_btn.setObjectName(u"save_preset_btn")
        self.save_preset_btn.setFont(font)

        self.horizontalLayout.addWidget(self.save_preset_btn)

        self.horizontalSpacer_5 = QSpacerItem(98, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_5)

        self.one_three_type_box = QComboBox(power_calibration)
        self.one_three_type_box.addItem("")
        self.one_three_type_box.addItem("")
        self.one_three_type_box.addItem("")
        self.one_three_type_box.setObjectName(u"one_three_type_box")
        self.one_three_type_box.setFont(font)

        self.horizontalLayout.addWidget(self.one_three_type_box)

        self.cali_data_box = QComboBox(power_calibration)
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.setObjectName(u"cali_data_box")
        self.cali_data_box.setFont(font)

        self.horizontalLayout.addWidget(self.cali_data_box)

        self.pushB_start_cali = QPushButton(power_calibration)
        self.pushB_start_cali.setObjectName(u"pushB_start_cali")
        self.pushB_start_cali.setFont(font)

        self.horizontalLayout.addWidget(self.pushB_start_cali)

        self.cali_step_edit = QLineEdit(power_calibration)
        self.cali_step_edit.setObjectName(u"cali_step_edit")
        self.cali_step_edit.setMaximumSize(QSize(180, 16777215))
        self.cali_step_edit.setFont(font)
        self.cali_step_edit.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout.addWidget(self.cali_step_edit)

        self.horizontalSpacer_10 = QSpacerItem(98, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_10)


        self.gridLayout_2.addLayout(self.horizontalLayout, 1, 0, 1, 1)

        self.verticalSpacer = QSpacerItem(20, 58, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.gridLayout_2.addItem(self.verticalSpacer, 2, 0, 1, 1)

        QWidget.setTabOrder(self.lineEdit_a_v, self.lineEdit_b_v)
        QWidget.setTabOrder(self.lineEdit_b_v, self.lineEdit_c_v)
        QWidget.setTabOrder(self.lineEdit_c_v, self.lineEdit_a_i)
        QWidget.setTabOrder(self.lineEdit_a_i, self.lineEdit_b_i)
        QWidget.setTabOrder(self.lineEdit_b_i, self.lineEdit_c_i)
        QWidget.setTabOrder(self.lineEdit_c_i, self.lineEdit_a_p)
        QWidget.setTabOrder(self.lineEdit_a_p, self.lineEdit_b_p)
        QWidget.setTabOrder(self.lineEdit_b_p, self.lineEdit_c_p)
        QWidget.setTabOrder(self.lineEdit_c_p, self.lineEdit_a_q)
        QWidget.setTabOrder(self.lineEdit_a_q, self.lineEdit_b_q)
        QWidget.setTabOrder(self.lineEdit_b_q, self.lineEdit_c_q)
        QWidget.setTabOrder(self.lineEdit_c_q, self.lineEdit_a_s)
        QWidget.setTabOrder(self.lineEdit_a_s, self.lineEdit_b_s)
        QWidget.setTabOrder(self.lineEdit_b_s, self.lineEdit_c_s)
        QWidget.setTabOrder(self.lineEdit_c_s, self.lineEdit_a_z)
        QWidget.setTabOrder(self.lineEdit_a_z, self.lineEdit_b_z)
        QWidget.setTabOrder(self.lineEdit_b_z, self.lineEdit_c_z)
        QWidget.setTabOrder(self.lineEdit_c_z, self.one_three_type_box)
        QWidget.setTabOrder(self.one_three_type_box, self.cali_data_box)
        QWidget.setTabOrder(self.cali_data_box, self.pushB_start_cali)
        QWidget.setTabOrder(self.pushB_start_cali, self.cali_step_edit)

        self.retranslateUi(power_calibration)

        QMetaObject.connectSlotsByName(power_calibration)
    # setupUi

    def retranslateUi(self, power_calibration):
        power_calibration.setWindowTitle(QCoreApplication.translate("power_calibration", u"\u529f\u7387\u6821\u8868", None))
        self.label_11.setText("")
        self.label_v.setText(QCoreApplication.translate("power_calibration", u"\u7535\u538b", None))
        self.label_i.setText(QCoreApplication.translate("power_calibration", u"\u7535\u6d41", None))
        self.label_p.setText(QCoreApplication.translate("power_calibration", u"\u6709\u529f\u529f\u7387", None))
        self.label_q.setText(QCoreApplication.translate("power_calibration", u"\u65e0\u529f\u529f\u7387", None))
        self.label_s.setText(QCoreApplication.translate("power_calibration", u"\u89c6\u5728\u529f\u7387", None))
        self.label_z.setText(QCoreApplication.translate("power_calibration", u"\u96f6\u7ebf\u7535\u6d41", None))
        self.label_a.setText(QCoreApplication.translate("power_calibration", u"A", None))
        self.label_b.setText(QCoreApplication.translate("power_calibration", u"B", None))
        self.label_c.setText(QCoreApplication.translate("power_calibration", u"C", None))
        self.label.setText(QCoreApplication.translate("power_calibration", u"\u9884\u8bbe\u65b9\u6848\uff1a", None))
        self.load_preset_btn.setText(QCoreApplication.translate("power_calibration", u"\u52a0\u8f7d\u65b9\u6848", None))
        self.save_preset_btn.setText(QCoreApplication.translate("power_calibration", u"\u4fdd\u5b58\u5f53\u524d\u65b9\u6848", None))
        self.one_three_type_box.setItemText(0, QCoreApplication.translate("power_calibration", u"\u5355\u76f8\u6821\u8868L", None))
        self.one_three_type_box.setItemText(1, QCoreApplication.translate("power_calibration", u"\u5355\u76f8\u6821\u8868N", None))
        self.one_three_type_box.setItemText(2, QCoreApplication.translate("power_calibration", u"\u4e09\u76f8\u6821\u8868", None))

        self.cali_data_box.setItemText(0, QCoreApplication.translate("power_calibration", u"\u529f\u7387\u589e\u76ca\u6821\u51c6", None))
        self.cali_data_box.setItemText(1, QCoreApplication.translate("power_calibration", u"\u76f8\u4f4d\u6821\u51c61", None))
        self.cali_data_box.setItemText(2, QCoreApplication.translate("power_calibration", u"\u76f8\u4f4d\u6821\u51c62", None))
        self.cali_data_box.setItemText(3, QCoreApplication.translate("power_calibration", u"\u76f8\u4f4d\u6821\u51c63", None))
        self.cali_data_box.setItemText(4, QCoreApplication.translate("power_calibration", u"\u529f\u7387\u504f\u79fb\u6821\u51c6", None))
        self.cali_data_box.setItemText(5, QCoreApplication.translate("power_calibration", u"\u7535\u6d41\u504f\u79fb\u6821\u51c6", None))
        self.cali_data_box.setItemText(6, QCoreApplication.translate("power_calibration", u"\u5c0f\u4fe1\u53f7\u6821\u51c6", None))
        self.cali_data_box.setItemText(7, QCoreApplication.translate("power_calibration", u"\u5176\u4ed6\u70b9\u6821\u51c6", None))

        self.cali_data_box.setCurrentText(QCoreApplication.translate("power_calibration", u"\u529f\u7387\u589e\u76ca\u6821\u51c6", None))
        self.pushB_start_cali.setText(QCoreApplication.translate("power_calibration", u"\u6267\u884c", None))
        self.cali_step_edit.setText("")
        self.cali_step_edit.setPlaceholderText(QCoreApplication.translate("power_calibration", u"#\u6b65\u9aa4\uff1a\u8f93\u5165\u6570\u5b57", None))
    # retranslateUi

