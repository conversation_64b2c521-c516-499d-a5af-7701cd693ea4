<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>main_page</class>
 <widget class="QWidget" name="main_page">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>967</width>
    <height>1000</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>校表软件</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="0">
    <layout class="QGridLayout" name="gridLayout">
     <item row="0" column="0">
      <widget class="QComboBox" name="port_box">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="editable">
        <bool>false</bool>
       </property>
       <property name="currentText">
        <string>com1</string>
       </property>
       <item>
        <property name="text">
         <string>com1</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>com2</string>
        </property>
       </item>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QComboBox" name="band_rate_box">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="editable">
        <bool>false</bool>
       </property>
       <property name="currentText">
        <string>2400</string>
       </property>
       <item>
        <property name="text">
         <string>2400</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>4800</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>9600</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>115200</string>
        </property>
       </item>
      </widget>
     </item>
     <item row="0" column="2">
      <widget class="QComboBox" name="even_box">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="editable">
        <bool>false</bool>
       </property>
       <property name="currentText">
        <string>偶校验</string>
       </property>
       <property name="currentIndex">
        <number>0</number>
       </property>
       <item>
        <property name="text">
         <string>偶校验</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>无校验</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>奇校验</string>
        </property>
       </item>
      </widget>
     </item>
     <item row="0" column="3">
      <widget class="QComboBox" name="data_len_box">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="editable">
        <bool>false</bool>
       </property>
       <property name="currentText">
        <string>8位</string>
       </property>
       <item>
        <property name="text">
         <string>8位</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>7位</string>
        </property>
       </item>
      </widget>
     </item>
     <item row="0" column="4">
      <widget class="QComboBox" name="stop_bits_box">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="editable">
        <bool>false</bool>
       </property>
       <property name="currentText">
        <string>1位</string>
       </property>
       <item>
        <property name="text">
         <string>1位</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>1.5位</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>2位</string>
        </property>
       </item>
      </widget>
     </item>
     <item row="0" column="5" colspan="2">
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="1" column="0">
      <widget class="QPushButton" name="refresh_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>刷新串口</string>
       </property>
       <property name="icon">
        <iconset theme="QIcon::ThemeIcon::ViewRefresh"/>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QPushButton" name="set_serial_1_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>串口方案115200</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="2">
      <widget class="QPushButton" name="set_serial_2_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>串口方案2400</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="3">
      <widget class="QPushButton" name="open_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>打开串口</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="4" colspan="2">
      <widget class="QPushButton" name="read_addr_btn">
       <property name="maximumSize">
        <size>
         <width>150</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>读设备地址</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="6" colspan="2">
      <widget class="QLineEdit" name="addr_edit">
       <property name="maximumSize">
        <size>
         <width>180</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>AAAAAAAAAAAA</string>
       </property>
       <property name="placeholderText">
        <string>#设备地址</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QPushButton" name="power_cali_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>功率校表</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QPushButton" name="err_cali_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>误差校表</string>
       </property>
      </widget>
     </item>
     <item row="2" column="2">
      <widget class="QPushButton" name="read_meter_chip_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>读写计量芯片</string>
       </property>
      </widget>
     </item>
     <item row="2" column="3">
      <widget class="QPushButton" name="cali_data_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>校表数据</string>
       </property>
      </widget>
     </item>
     <item row="2" column="4">
      <widget class="QPushButton" name="db_viewer_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>数据库</string>
       </property>
      </widget>
     </item>
     <item row="2" column="5" colspan="2">
      <widget class="QPushButton" name="dlt645_sub_33_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>dlt645数据处理</string>
       </property>
      </widget>
     </item>
     <item row="2" column="7">
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <widget class="QStackedWidget" name="stackedWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>180</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>350</height>
      </size>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="clear_log_btn">
       <property name="font">
        <font>
         <pointsize>15</pointsize>
        </font>
       </property>
       <property name="text">
        <string>清除log</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="3" column="0">
    <widget class="QTextEdit" name="log_edit">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>500</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>13</pointsize>
      </font>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>port_box</tabstop>
  <tabstop>band_rate_box</tabstop>
  <tabstop>data_len_box</tabstop>
  <tabstop>even_box</tabstop>
  <tabstop>stop_bits_box</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
