# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'database_viewer.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>urs<PERSON>,
    <PERSON><PERSON><PERSON>, Q<PERSON>ontData<PERSON>, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QGroupBox, QHBoxLayout,
    QHeaderView, QLabel, QLineEdit, QPushButton,
    QSizePolicy, QSpacerItem, QTableView, QTextEdit,
    QVBoxLayout, QWidget)

class Ui_database_viewer(object):
    def setupUi(self, database_viewer):
        if not database_viewer.objectName():
            database_viewer.setObjectName(u"database_viewer")
        database_viewer.resize(1269, 250)
        self.verticalLayout = QVBoxLayout(database_viewer)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.horizontalLayout_1 = QHBoxLayout()
        self.horizontalLayout_1.setObjectName(u"horizontalLayout_1")
        self.open_db_btn = QPushButton(database_viewer)
        self.open_db_btn.setObjectName(u"open_db_btn")

        self.horizontalLayout_1.addWidget(self.open_db_btn)

        self.db_path_line_edit = QLineEdit(database_viewer)
        self.db_path_line_edit.setObjectName(u"db_path_line_edit")
        self.db_path_line_edit.setReadOnly(True)

        self.horizontalLayout_1.addWidget(self.db_path_line_edit)


        self.verticalLayout.addLayout(self.horizontalLayout_1)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.label = QLabel(database_viewer)
        self.label.setObjectName(u"label")

        self.horizontalLayout_2.addWidget(self.label)

        self.table_selector_combo = QComboBox(database_viewer)
        self.table_selector_combo.setObjectName(u"table_selector_combo")

        self.horizontalLayout_2.addWidget(self.table_selector_combo)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer)


        self.verticalLayout.addLayout(self.horizontalLayout_2)

        self.groupBox = QGroupBox(database_viewer)
        self.groupBox.setObjectName(u"groupBox")
        self.verticalLayout_2 = QVBoxLayout(self.groupBox)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.sql_query_textedit = QTextEdit(self.groupBox)
        self.sql_query_textedit.setObjectName(u"sql_query_textedit")

        self.verticalLayout_2.addWidget(self.sql_query_textedit)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_2)

        self.execute_query_btn = QPushButton(self.groupBox)
        self.execute_query_btn.setObjectName(u"execute_query_btn")

        self.horizontalLayout.addWidget(self.execute_query_btn)


        self.verticalLayout_2.addLayout(self.horizontalLayout)


        self.verticalLayout.addWidget(self.groupBox)

        self.label_2 = QLabel(database_viewer)
        self.label_2.setObjectName(u"label_2")

        self.verticalLayout.addWidget(self.label_2)

        self.results_tableview = QTableView(database_viewer)
        self.results_tableview.setObjectName(u"results_tableview")

        self.verticalLayout.addWidget(self.results_tableview)

        self.status_label = QLabel(database_viewer)
        self.status_label.setObjectName(u"status_label")

        self.verticalLayout.addWidget(self.status_label)


        self.retranslateUi(database_viewer)

        QMetaObject.connectSlotsByName(database_viewer)
    # setupUi

    def retranslateUi(self, database_viewer):
        database_viewer.setWindowTitle(QCoreApplication.translate("database_viewer", u"Database Viewer", None))
        self.open_db_btn.setText(QCoreApplication.translate("database_viewer", u"\u6253\u5f00\u6570\u636e\u5e93\u6587\u4ef6", None))
        self.db_path_line_edit.setPlaceholderText(QCoreApplication.translate("database_viewer", u"\u672a\u52a0\u8f7d\u6570\u636e\u5e93\u6587\u4ef6", None))
        self.label.setText(QCoreApplication.translate("database_viewer", u"\u6570\u636e\u8868:", None))
        self.groupBox.setTitle(QCoreApplication.translate("database_viewer", u"\u81ea\u5b9a\u4e49\u67e5\u8be2", None))
        self.sql_query_textedit.setPlaceholderText(QCoreApplication.translate("database_viewer", u"\u5728\u6b64\u8f93\u5165 SELECT \u67e5\u8be2\u8bed\u53e5...", None))
        self.execute_query_btn.setText(QCoreApplication.translate("database_viewer", u"\u6267\u884c\u67e5\u8be2", None))
        self.label_2.setText(QCoreApplication.translate("database_viewer", u"\u67e5\u8be2\u7ed3\u679c:", None))
        self.status_label.setText(QCoreApplication.translate("database_viewer", u"\u72b6\u6001: \u672a\u8fde\u63a5", None))
    # retranslateUi

