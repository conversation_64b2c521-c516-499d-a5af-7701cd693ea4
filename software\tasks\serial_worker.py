from PySide6.QtCore import QObject, Signal, Slot, QByteArray
from PySide6.QtSerialPort import QSerialPort

from software.protocol import DLT645_2007_s

class SerialWorker(QObject):
    """
    一个纯粹的串口工作器，在独立的线程中运行。
    它负责所有底层的串口I/O和字节流解析。
    它不了解任何UI界面，只通过信号与外界通信。
    """

    frame_decoded = Signal(dict)
    error_occurred = Signal(str)
    log_message = Signal(str, str)

    def __init__(self, protocol_handler: DLT645_2007_s):
        super().__init__()
        self.protocol_handler = protocol_handler
        self.serial_port = QSerialPort()
        self.receive_buffer = bytearray()
        self._is_port_open = False

    @Slot(dict) 
    def start_port(self, port_config: dict):
        if self._is_port_open:
            self.log_message.emit("端口已经打开，请先关闭。", "orange")
            return

        try:
            self.serial_port.setPortName(port_config.get("port_name"))
            self.serial_port.setBaudRate(port_config.get("baud_rate", 9600))

            # --- 完整地设置数据位、校验位、停止位 ---
            data_bits_map = {"8位": QSerialPort.DataBits.Data8, "7位": QSerialPort.DataBits.Data7}
            data_bits = data_bits_map.get(port_config.get("data_bits_str", "8位"), QSerialPort.DataBits.Data8)
            self.serial_port.setDataBits(data_bits)

            parity_map = {"无校验": QSerialPort.Parity.NoParity, "奇校验": QSerialPort.Parity.OddParity, "偶校验": QSerialPort.Parity.EvenParity}
            parity = parity_map.get(port_config.get("parity_str", "无校验"), QSerialPort.Parity.NoParity)
            self.serial_port.setParity(parity)

            stop_bits_map = {"1": QSerialPort.StopBits.OneStop, "2": QSerialPort.StopBits.TwoStop}
            stop_bits = stop_bits_map.get(port_config.get("stop_bits_str", "1"), QSerialPort.StopBits.OneStop)
            self.serial_port.setStopBits(stop_bits)
            # --- 设置结束 ---

            if self.serial_port.open(QSerialPort.OpenModeFlag.ReadWrite):
                self._is_port_open = True
                self.serial_port.readyRead.connect(self.on_ready_read)
                self.log_message.emit(f"端口 {port_config.get('port_name')} 已打开。", "green")
            else:
                self.error_occurred.emit(f"无法打开串口: {self.serial_port.errorString()}")
        except Exception as e:
            self.error_occurred.emit(f"打开端口时发生意外错误: {e}")

    @Slot()
    def stop_port(self):
        if self._is_port_open:
            self.serial_port.close()
            self._is_port_open = False
            self.log_message.emit("端口已关闭。", "black")

    @Slot(QByteArray)  # type: ignore
    def send_data(self, data: bytes):
        if self._is_port_open:
            self.serial_port.write(data)
        else:
            self.error_occurred.emit("发送失败：端口未打开。")

    @Slot()
    def on_ready_read(self):
        if not self._is_port_open:
            return
        
        new_data = self.serial_port.readAll().data()
        if not new_data:
            return
            
        hex_data = " ".join([f"{x:02X}" for x in new_data])
        self.log_message.emit(f"原始接收: {hex_data}", "#DAA520") # 使用暗金色显示原始数据
        self.receive_buffer.extend(new_data)
        
        while True:
            if self._parse_buffer() is False:
                break

    def _parse_buffer(self) -> bool:
        start_index = self.receive_buffer.find(b'\x68')
        if start_index == -1:
            return False

        if len(self.receive_buffer) < start_index + 10:
            return False

        data_length = self.receive_buffer[start_index + 9]
        frame_length = 12 + data_length

        if len(self.receive_buffer) < start_index + frame_length:
            return False

        potential_frame = self.receive_buffer[start_index : start_index + frame_length]

        if potential_frame[-1] != 0x16:
            del self.receive_buffer[:start_index + 1]
            return True

        try:
            decoded = self.protocol_handler.dlt645_parse_response(potential_frame)
            if isinstance(decoded, dict):
                self.frame_decoded.emit(decoded)
            elif isinstance(decoded, str):
                self.log_message.emit(f"协议解析消息: {decoded}", "orange")
        except Exception as e:
            self.error_occurred.emit(f"协议解析时发生错误: {e}")
        
        del self.receive_buffer[:start_index + frame_length]
        return True