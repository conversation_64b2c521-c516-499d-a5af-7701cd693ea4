software/yuanshen_jiemi.py

# Python virtual environment
.venv/
venv/
env/
.env/
ENV/

# Python byte-compiled files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/

# IDE settings
.idea/
*.swp
*.swo

# Environment variables
.env
.env.local

# Qt Creator / Designer
*.user
*.autosave

# Logs and databases
*.log
*.sqlite
*.db

# OS generated files
.DS_Store
.DS_Store?
.*_*
.Spotlight-V100
.Trashes
Thumbs.db
ehthumbs.db

*.exe

# node_modules
node_modules/

#pack.json
package.json
package-lock.json