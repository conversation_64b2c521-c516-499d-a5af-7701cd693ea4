[tool.pyright]
# 忽略 PySide6 相关的类型检查错误
reportGeneralTypeIssues = false
reportOptionalMemberAccess = false
reportOptionalSubscript = false
reportPrivateImportUsage = false

# 针对 PySide6 Slot 装饰器的特殊处理
reportIncompatibleMethodOverride = false
reportIncompatibleVariableOverride = false

[tool.pylsp-mypy]
enabled = true
live_mode = true
strict = false

[tool.mypy]
# 忽略 PySide6 相关模块的类型检查
[[tool.mypy.overrides]]
module = "PySide6.*"
ignore_errors = true

# 忽略装饰器相关的类型错误
disable_error_code = ["misc", "arg-type"]
