r"""
Author: li <EMAIL>
Date: 2024-12-30 22:29:15
LastEditors: li <EMAIL>
LastEditTime: 2025-03-09 22:46:30
FilePath: \单三相电表测试软件\software\protocol.py
"""

from enum import Enum
from typing import Dict, Any, Optional


class DLT645_Constants(Enum):
    HEADER = 0x68  # 假设帧头是 0x68
    FOOTER = 0x16  # 假设帧尾是 0x16
    TRUE_BACK = 0x80  # 假设正确返回帧头
    ERR_BACK = 0xC0  # 假设错误返回帧头
    MIN_LEN = 12
    HEADER_START_INDEX = 0  # 帧头起始位置
    HEADER_REPEAT_INDEX = 7  # 帧头重复位置
    CLT_CODE_INDEX = 8
    DATA_LEN_INDEX = 9
    DATA_INDEX = 10
    SUM_CHECK_INDEX = -2
    END_INDEX = -1

class DLT645_2007_s:
    def __init__(self, parent: Optional[Any] = None):
        # 定义一些常量或配置
        self.parent = parent

    def dlt645_build_cmd(self, params: Dict[str, Any]) -> bytearray:
        """
        组包函数：根据 DLT645-2007 协议构建命令帧。

        Args:
            params: 包含地址、命令码和数据的字典

        Returns:
            bytearray: 构建好的命令帧
        """
        # 初始化帧
        frame = bytearray()

        # 添加帧头
        frame.append(DLT645_Constants.HEADER.value)

        # 添加地址（假设地址长度固定为 6 字节）
        addr_len = len(params["addr"])
        if addr_len != 6:
            raise ValueError("地址长度错误")

        frame.extend(params["addr"])

        # 添加帧头重复（某些协议可能需要重复帧头）
        frame.append(DLT645_Constants.HEADER.value)

        # 添加命令码
        frame.append(params["cmd_code"])

        # 添加数据长度（假设长度为数据部分的字节数）
        modified_data = b"".join(bytes([(b + 0x33) & 0xFF]) for b in params["data"])

        frame.append(len(modified_data))

        # 添加数据
        frame.extend(modified_data)

        # 添加校验和（假设校验和为所有数据的累加和取低字节）
        frame.append(sum(frame[0:]) & 0xFF)

        # 添加帧尾
        frame.append(DLT645_Constants.FOOTER.value)

        return frame

    def dlt645_parse_response(self, response: bytes) -> Dict[str, Any]:
        """
        解析函数：根据 DLT645-2007 协议解析响应帧。

        Returns:
            Dict[str, Any]: 包含地址、命令码和数据的字典
        """
        if len(response) < DLT645_Constants.MIN_LEN.value:
            raise ValueError("响应帧长度不足")

        # 检查帧头
        if (
            response[DLT645_Constants.HEADER_START_INDEX.value] != DLT645_Constants.HEADER.value
            or response[DLT645_Constants.HEADER_REPEAT_INDEX.value] != DLT645_Constants.HEADER.value
        ):
            raise ValueError("帧头不匹配")

        # 检查帧尾
        if response[-1] != DLT645_Constants.FOOTER.value:
            raise ValueError("帧尾不匹配")

        # 提取地址
        addr = response[1:7]

        # 提取命令码
        cmd_code = response[DLT645_Constants.CLT_CODE_INDEX.value]

        # 提取数据长度
        data_length = response[DLT645_Constants.DATA_LEN_INDEX.value]

        # 检查数据长度是否有效
        if len(response) != DLT645_Constants.MIN_LEN.value + data_length:
            raise ValueError("数据长度无效")

        # 提取校验和
        checksum = response[DLT645_Constants.SUM_CHECK_INDEX.value]
        calculated_checksum = sum(response[0 : DLT645_Constants.SUM_CHECK_INDEX.value]) & 0xFF

        if checksum != calculated_checksum:
            raise ValueError("校验和错误")

        # 提取数据
        data = response[DLT645_Constants.DATA_INDEX.value : DLT645_Constants.DATA_INDEX.value + data_length]

        # 去掉数据部分的 0x33
        actual_data = data[1:] if data and data[0] == 0x33 else data

        return {"addr": addr, "cmd_code": cmd_code, "data": actual_data}


if __name__ == "__main__":
    addr = b"\x01\x02\x03\x04\x05\x06"
    cmd_code = 0x03
    data = b"\x03\x10\x36\x4b\xe1"

    params: Dict[str, Any] = {"addr": addr, "cmd_code": cmd_code, "data": data}

    # 创建 DLT645_2007 类的实例
    protocol_instance = DLT645_2007_s()

    # 使用实例调用方法
    cmd_frame = protocol_instance.dlt645_build_cmd(params)
    
    print("".join([f"{x:02X} " for x in cmd_frame]))

    response_data = protocol_instance.dlt645_parse_response(cmd_frame)

    # 示例：打印解析结果
    if isinstance(response_data, str):
        print(f"错误：{response_data}")
        exit(1)

    print("\n地址")
    print("".join([f"{x:02X} " for x in response_data['addr']]))

    print("\n命令码")
    
    print("\n数据")
    print("".join([f"{x:02X} " for x in response_data['data']]))

    # print(f"\n地址: {response_data['addr']}, 命令码: {response_data['cmd_code']}, 数据: {response_data['data']}")
